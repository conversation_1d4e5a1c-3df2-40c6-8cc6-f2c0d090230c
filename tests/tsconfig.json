{"extends": "../tsconfig.json", "compilerOptions": {"types": ["vitest/globals", "@testing-library/jest-dom", "node"], "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "jsx": "react-jsx"}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../src/**/*.ts", "../src/**/*.tsx"], "exclude": ["node_modules", "dist", "build"]}