# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# webstorm
.idea

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/test-results
/playwright-report
debug-dashboard.png

# production
/build
/certs

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.env
.env.example

package-lock.json
