/**
 * CalendarDatePicker component exports
 * 
 * Refactored from single 319-line file to modular architecture:
 * - CalendarDatePicker.tsx: Main component (~80 lines)
 * - CalendarHeader.tsx: Month navigation header
 * - CalendarGrid.tsx: Calendar days grid
 * - CalendarFooter.tsx: Footer actions
 * - useCalendarState.ts: State management hook
 * - utils.ts: Utility functions
 * - types.ts: TypeScript interfaces
 * 
 * Benefits:
 * ✅ 75% reduction in main component size (319 → ~80 lines)
 * ✅ Single Responsibility Principle compliance
 * ✅ Better testability with focused components
 * ✅ Improved maintainability and readability
 * ✅ Reusable components and utilities
 * ✅ Type safety with TypeScript
 */

export { default } from './CalendarDatePicker';
export { default as CalendarDatePicker } from './CalendarDatePicker';
export { CalendarHeader } from './CalendarHeader';
export { CalendarGrid } from './CalendarGrid';
export { CalendarFooter } from './CalendarFooter';
export { useCalendarState } from './useCalendarState';
export * from './utils';
export * from './types';
