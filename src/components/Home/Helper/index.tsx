import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  useTheme,
  useMediaQuery,
  Fab,
  Tooltip,
  CircularProgress,
  Alert,
  IconButton
} from '@mui/material';
import HelpIcon from '@mui/icons-material/Help';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import api from '../../../services';

interface ChatMessage {
  id: string;
  sender: 'user' | 'bot';
  text: string;
  isStreaming?: boolean;
  timestamp: Date;
  error?: boolean;
}

interface FloatingChatHelperProps {
  darkMode?: boolean;
  toggleDarkMode?: () => void;
  isMobile?: boolean;
}

interface ChatState {
  isLoading: boolean;
  error: string | null;
  hasError: boolean;
}

const FloatingChatHelper: React.FC<FloatingChatHelperProps> = () => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [chatOpen, setChatOpen] = useState<boolean>(false);
  const [chatInput, setChatInput] = useState<string>('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatState, setChatState] = useState<ChatState>({
    isLoading: false,
    error: null,
    hasError: false
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Generate unique message ID
  const generateMessageId = useCallback((): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }, []);

  // Clear error state
  const clearError = useCallback(() => {
    setChatState(prev => ({ ...prev, error: null, hasError: false }));
  }, []);


  const handleChatToggle = useCallback(() => {
    setChatOpen(!chatOpen);
    if (!chatOpen) {
      clearError();
    }
  }, [chatOpen, clearError]);

  const handleChatSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();

    if (!chatInput.trim() || chatState.isLoading) {
      return;
    }

    const userMessage = chatInput.trim();
    const userMessageId = generateMessageId();
    const botMessageId = generateMessageId();

    // Clear any previous errors
    clearError();

    // Add user message
    const userMsg: ChatMessage = {
      id: userMessageId,
      sender: 'user',
      text: userMessage,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMsg]);
    setChatInput('');

    // Set loading state
    setChatState(prev => ({ ...prev, isLoading: true, error: null }));

    // Add streaming bot message placeholder
    const botMsg: ChatMessage = {
      id: botMessageId,
      sender: 'bot',
      text: '',
      isStreaming: true,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, botMsg]);

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const fullResponse = await api.chatbot.help(
        userMessage,
        (_token: string, fullText: string) => {
          setChatMessages(prev => {
            const updated = [...prev];
            const lastMsgIndex = updated.findIndex(msg => msg.id === botMessageId);
            if (lastMsgIndex !== -1 && updated[lastMsgIndex].isStreaming) {
              updated[lastMsgIndex] = {
                ...updated[lastMsgIndex],
                text: fullText,
              };
            }
            return updated;
          });
        }
      );

      // Finalize the bot message
      setChatMessages(prev => {
        const updated = [...prev];
        const lastMsgIndex = updated.findIndex(msg => msg.id === botMessageId);
        if (lastMsgIndex !== -1) {
          updated[lastMsgIndex] = {
            id: botMessageId,
            sender: 'bot',
            text: fullResponse,
            isStreaming: false,
            timestamp: new Date()
          };
        }
        return updated;
      });

    } catch (error: any) {
      console.error('Error calling ChatBot:', error);

      // Remove the streaming placeholder and add error message
      setChatMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== botMessageId);
        const errorMsg: ChatMessage = {
          id: generateMessageId(),
          sender: 'bot',
          text: "I'm sorry, I encountered an error processing your request. Please try again later.",
          timestamp: new Date(),
          error: true
        };
        return [...filtered, errorMsg];
      });

      setChatState(prev => ({
        ...prev,
        error: error.message || 'An unexpected error occurred',
        hasError: true
      }));
    } finally {
      setChatState(prev => ({ ...prev, isLoading: false }));
      abortControllerRef.current = null;
    }
  }, [chatInput, chatState.isLoading, generateMessageId, clearError]);

  return (
      <Box sx={{
        position: 'fixed',
        bottom: { xs: 16, sm: 20 },
        right: { xs: 16, sm: 20 },
        zIndex: 1000
      }}>
        <Tooltip title="Chat with AI Assistant" arrow placement="left">
          <Fab
              color="primary"
              aria-label="chat"
              onClick={handleChatToggle}
              size={isSmallMobile ? "medium" : "large"}
              disabled={chatState.isLoading}
              sx={{
                bgcolor: chatOpen ? (isDarkMode ? '#00796b' : 'secondary.main') : (isDarkMode ? '#00897b' : 'primary.main'),
                boxShadow: isDarkMode ? '0 2px 12px rgba(0, 230, 180, 0.4)' : '0 2px 8px rgba(0, 0, 0, 0.2)',
                width: { xs: 48, sm: 56 },
                height: { xs: 48, sm: 56 },
                '&.Mui-disabled': {
                  bgcolor: isDarkMode ? '#424242' : '#e0e0e0'
                }
              }}
          >
            {chatState.isLoading ? (
              <CircularProgress size={isSmallMobile ? 20 : 24} color="inherit" />
            ) : (
              <HelpIcon sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }}/>
            )}
          </Fab>
        </Tooltip>

        {chatOpen && (
            <Paper
                elevation={isDarkMode ? 6 : 3}
                sx={{
                  position: 'absolute',
                  bottom: { xs: 60, sm: 70 },
                  right: 0,
                  width: { xs: 280, sm: 300, md: 320 },
                  height: { xs: 350, sm: 400 },
                  display: 'flex',
                  flexDirection: 'column',
                  overflow: 'hidden',
                  bgcolor: isDarkMode ? 'rgba(38, 50, 56, 0.95)' : 'background.paper',
                  border: isDarkMode ? '1px solid rgba(0, 230, 180, 0.15)' : 'none',
                  borderRadius: 2
                }}
            >
              <Box
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    p: 2,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
              >
                <Typography variant="subtitle1" sx={{fontWeight: 'medium'}}>
                  Help Assistant
                </Typography>
                <IconButton
                  size="small"
                  onClick={handleChatToggle}
                  sx={{ color: 'primary.contrastText' }}
                  aria-label="close chat"
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>

              {/* Error Alert */}
              {chatState.hasError && chatState.error && (
                <Alert
                  severity="error"
                  onClose={clearError}
                  sx={{ m: 1, fontSize: '0.875rem' }}
                >
                  {chatState.error}
                </Alert>
              )}

              <Box
                  sx={{
                    flexGrow: 1,
                    p: 2,
                    overflowY: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    bgcolor: 'background.paper',
                  }}
              >
                {chatMessages.map((msg) => (
                    <Box
                        key={msg.id}
                        sx={{
                          p: 1.5,
                          borderRadius: 2,
                          maxWidth: '80%',
                          alignSelf: msg.sender === 'user' ? 'flex-end' : 'flex-start',
                          bgcolor: msg.error
                            ? (isDarkMode ? '#d32f2f' : '#ffebee')
                            : msg.sender === 'user'
                              ? theme.palette.primary.light
                              : (isDarkMode ? theme.palette.grey[700] : theme.palette.grey[200]),
                          color: msg.error
                            ? (isDarkMode ? '#fff' : '#d32f2f')
                            : msg.sender === 'user'
                              ? theme.palette.primary.contrastText
                              : theme.palette.text.primary,
                          boxShadow: isDarkMode ? '0 1px 3px rgba(0, 0, 0, 0.3)' : 'none',
                          border: msg.error ? `1px solid ${isDarkMode ? '#f44336' : '#f44336'}` : 'none',
                          position: 'relative'
                        }}
                    >
                      <Typography variant="body2" sx={{color: 'inherit'}}>
                        {msg.text || (msg.isStreaming ? 'Thinking...' : '')}
                      </Typography>
                      {msg.isStreaming && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <CircularProgress size={12} sx={{ mr: 1 }} />
                          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                            Typing...
                          </Typography>
                        </Box>
                      )}
                    </Box>
                ))}
                {chatMessages.length === 0 && !chatState.isLoading && (
                    <Typography
                        variant="body2"
                        sx={{
                          textAlign: 'center',
                          mt: 10,
                          color: 'text.secondary',
                        }}
                    >
                      Hi there! How can I help you today?
                    </Typography>
                )}
                <div ref={messagesEndRef} />
              </Box>

              <Box
                  component="form"
                  onSubmit={handleChatSubmit}
                  sx={{
                    p: 1,
                    display: 'flex',
                    borderTop: `1px solid ${theme.palette.divider}`,
                    gap: 1
                  }}
              >
                <TextField
                    fullWidth
                    size="small"
                    placeholder={chatState.isLoading ? "Processing..." : "Type your message..."}
                    variant="outlined"
                    value={chatInput}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setChatInput(e.target.value)}
                    disabled={chatState.isLoading}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleChatSubmit(e as any);
                      }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&.Mui-disabled': {
                          bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'
                        }
                      }
                    }}
                />
                <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={chatState.isLoading || !chatInput.trim()}
                    sx={{
                      minWidth: 'auto',
                      px: 2,
                      '&.Mui-disabled': {
                        bgcolor: isDarkMode ? '#424242' : '#e0e0e0'
                      }
                    }}
                    aria-label="send message"
                >
                  {chatState.isLoading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <SendIcon fontSize="small" />
                  )}
                </Button>
              </Box>
            </Paper>
        )}
      </Box>
  );
};

export default FloatingChatHelper;
export type { ChatMessage };
