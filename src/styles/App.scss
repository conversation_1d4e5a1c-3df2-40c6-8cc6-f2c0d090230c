// Main application styles
// Converted from App.css to SCSS with Sass features

// Variables
$font-family-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
  'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
$font-family-mono: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;

// Colors
$color-primary: #61dafb;
$color-dark-bg: #282c34;
$color-white: #ffffff;
$color-light-gray: #f5f5f5;
$color-medium-gray: #f2f2f2;
$color-border: #ddd;
$color-code-bg: rgba(0, 0, 0, 0.05);
$color-typing-dot: #888;

// Breakpoints
$breakpoint-mobile: 600px;

// Mixins
@mixin mobile-only {
  @media (max-width: $breakpoint-mobile) {
    @content;
  }
}

@mixin reduced-motion {
  @media (prefers-reduced-motion: no-preference) {
    @content;
  }
}

// Base styles
body {
  margin: 0;
  font-family: $font-family-system;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  @include mobile-only {
    font-size: 14px;
  }
}

// Markdown content styles
.markdown-content {
  img {
    max-width: 100%;
    height: auto;
  }

  pre {
    background-color: $color-light-gray;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.9rem;

    @include mobile-only {
      padding: 0.75rem;
      font-size: 0.8rem;
    }
  }

  code {
    font-family: $font-family-mono;
    background-color: $color-code-bg;
    padding: 0.2em 0.4em;
    border-radius: 3px;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    overflow-x: auto;
    display: block;

    @include mobile-only {
      font-size: 0.85rem;
    }
  }

  th,
  td {
    border: 1px solid $color-border;
    padding: 8px;
    text-align: left;

    @include mobile-only {
      padding: 6px;
    }
  }

  th {
    background-color: $color-medium-gray;
  }
}

// App component styles
.App {
  text-align: center;

  &-logo {
    height: 40vmin;
    pointer-events: none;

    @include reduced-motion {
      animation: App-logo-spin infinite 20s linear;
    }
  }

  &-header {
    background-color: $color-dark-bg;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: calc(10px + 2vmin);
    color: $color-white;
  }

  &-link {
    color: $color-primary;
  }
}

// Animations
@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Typing indicator
.typing-indicator {
  display: inline-flex;
  align-items: center;

  .dot {
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    margin: 0 2px;
    background-color: $color-typing-dot;
    animation: typing 1.5s infinite ease-in-out;

    @include mobile-only {
      width: 3px;
      height: 3px;
      margin: 0 1px;
    }

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;

    @include mobile-only {
      transform: translateY(-3px);
    }
  }
}

// Container responsive adjustments
@include mobile-only {
  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

// Additional color variables for notifications and UI
$color-urgent: #f44336;
$color-high-priority: #ff9800;
$color-normal: #4caf50;
$color-appointment: #2196f3;
$color-medical: #9c27b0;
$color-patient: #00bcd4;
$color-scrollbar: rgba(0, 0, 0, 0.2);
$color-scrollbar-hover: rgba(0, 0, 0, 0.3);
$color-shimmer-light: #f0f0f0;
$color-shimmer-dark: #e0e0e0;

// Smooth transitions for interactive elements
.MuiButton-root,
.MuiIconButton-root,
.MuiChip-root {
  transition: all 0.2s ease-in-out !important;
}

// Custom scrollbar for notification drawer
.notification-drawer-content {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-scrollbar;
    border-radius: 3px;

    &:hover {
      background: $color-scrollbar-hover;
    }
  }
}

// Loading animations
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, $color-shimmer-light 25%, $color-shimmer-dark 50%, $color-shimmer-light 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

// Notification styles
.notification {
  &-urgent {
    border-left: 4px solid $color-urgent !important;
    background-color: rgba($color-urgent, 0.05) !important;
  }

  &-high-priority {
    border-left: 4px solid $color-high-priority !important;
    background-color: rgba($color-high-priority, 0.05) !important;
  }

  &-normal {
    border-left: 4px solid $color-normal !important;
  }

  &-appointment {
    border-left-color: $color-appointment !important;
  }

  &-medical {
    border-left-color: $color-medical !important;
  }

  &-patient {
    border-left-color: $color-patient !important;
  }
}
