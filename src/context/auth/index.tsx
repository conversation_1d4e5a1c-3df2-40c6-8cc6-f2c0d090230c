import React, {createContext, useState, useContext, useEffect, useCallback, ReactNode} from 'react';
import api from '../../services';
import { AuthContextValue, User, UserRole } from '../../types';

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({children}) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true); // To check auth status on initial load

  // Function to process a received token (from OAuth or direct login)
  const processAuthToken = useCallback(async (token: string, tokenType: string = 'Bearer'): Promise<User> => {
    try {
      localStorage.setItem('authToken', token);
      localStorage.setItem('tokenType', tokenType);
      // Set Authorization header on the axios instance
      try {
        // Note: api is our service object, not axios instance
        // The actual axios configuration is handled in config.ts
      } catch (error) {
        console.warn("Could not set Authorization header:", error);
      }

      const userData = await api.auth.me(); // Fetch user details using the new token
      if (userData) {
        setCurrentUser(userData);
        // console.log('User details fetched and context updated:', userData);
        return userData;
      } else {
        throw new Error('User data not found after token processing.');
      }
    } catch (error) {
      console.error('Failed to process auth token or fetch user:', error);
      localStorage.removeItem('authToken');
      localStorage.removeItem('tokenType');
      // Note: Authorization header cleanup is handled by the axios interceptor
      setCurrentUser(null);
      throw error;
    }
  }, []);

  // Function to handle login (Email/Password)
  const login = async (email: string, password: string): Promise<User> => {
    try {
      const authData = await api.auth.login(email, password);

      if (authData && authData.accessToken && authData.user) {
        // processAuthToken will be called with the token, and it will fetch the user
        await processAuthToken(authData.accessToken, authData.tokenType || 'Bearer');
        // The user object is returned for immediate use if needed, though context will update
        return authData.user;
      } else {
        const errorMessage = 'Login failed: Invalid data structure from API.';
        console.error(errorMessage, authData);
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Login failed (AuthContext catch block):', (error as Error).message || error);
      throw error;
    }
  };

  const signup = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    role: UserRole = 'PATIENT',
    clinicName: string | null = null,
    existingClinicId: number | null = null
  ): Promise<any> => {
    try {
      console.log(`Attempting signup for ${email}`);

      const signupData: any = {
        email,
        password,
        firstName,
        lastName,
        role
      };

      if (clinicName) signupData.clinicName = clinicName;
      if (existingClinicId) signupData.existingClinicId = existingClinicId;

      const responseMessage = await api.auth.signup(signupData);

      console.log('Signup successful:', responseMessage);
      return {...signupData, emailVerificationPending: true, message: responseMessage};
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    }
  };

  const logout = useCallback(async (): Promise<void> => {
    try {
      await api.auth.logout();
    } catch (error) {
      console.error('Backend logout error (will proceed with client-side logout):', error);
    } finally {
      localStorage.removeItem('authToken');
      localStorage.removeItem('tokenType');
      // Note: Authorization header cleanup is handled by the axios interceptor
      setCurrentUser(null);
    }
  }, []);

  // ---- GOOGLE OAUTH (ID-TOKEN POPUP FLOW) ----
  // This helper receives the `credential` (ID token) generated by the
  // Google Identity Services popup. It forwards the token to the backend,
  // then stores & processes the internal JWT returned by our API.

  const googleIdLogin = useCallback(async (idToken: string): Promise<User> => {
    try {
      if (!idToken) throw new Error('Missing Google ID token');

      console.log('Received Google ID token, sending to backend for verification…');

      const backendTokenData = await api.auth.loginWithGoogleIdToken(idToken);

      if (backendTokenData && backendTokenData.accessToken) {
        await processAuthToken(backendTokenData.accessToken, backendTokenData.tokenType || 'Bearer');
        console.log('Google login successful – internal session established.');
        return backendTokenData.user || null;
      }
      throw new Error('Invalid response from backend after Google ID token exchange.');
    } catch (error) {
      console.error('Google ID-token login failed:', error);
      throw error;
    }
  }, [processAuthToken]);

  // Check authentication status on initial load
  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      // const tokenType = localStorage.getItem('tokenType') || 'Bearer'; // Not used
      if (token) {
        try {
          // Note: Authorization header is set by the axios interceptor
          const userData = await api.auth.me();
          if (userData) {
            setCurrentUser(userData);
          } else {
            throw new Error("User data not found during initial auth check");
          }
        } catch (error) {
          console.error('Token verification failed during initial load:', error);
          await logout();
        }
      } else {
        console.log('No token found during initial load.');
      }
      setLoading(false);
    };

    checkAuth();
  }, [logout, processAuthToken]); // Added processAuthToken to ensure it's stable if defined inside AuthProvider

  const value: AuthContextValue = {
    currentUser,
    isAuthenticated: !!currentUser,
    loading,
    login,
    signup,
    logout,
    googleIdLogin,
    processAuthToken
  };

  return (
      <AuthContext.Provider value={value}>
        {/* Render children only after initial auth check, or always show children and let them adapt */}
        {children}
      </AuthContext.Provider>
  );
};