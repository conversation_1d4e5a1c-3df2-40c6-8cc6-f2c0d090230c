{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Dentabot project", "main": "src/index.jsx", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/styled-engine-sc": "^7.1.1", "@mui/x-charts": "^8.5.1", "@mui/x-data-grid": "^8.5.1", "@mui/x-date-pickers": "^8.5.1", "@react-google-maps/api": "^2.20.6", "@react-oauth/google": "^0.12.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@toolpad/core": "^0.15.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "moment": "^2.30.1", "openai": "^5.3.0", "react": "^19.1.0", "react-big-calendar": "^1.19.2", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.2", "web-vitals": "^5.0.2"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:unit": "vitest tests/unit", "test:integration": "vitest tests/integration", "test:components": "vitest tests/components", "test:utils": "vitest tests/utils", "test:coverage": "vitest --coverage", "test:e2e": "playwright test tests/e2e", "test:e2e:ui": "playwright test tests/e2e --ui"}, "type": "module", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.28.0", "@playwright/test": "^1.53.0", "@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.5.2", "@vitest/ui": "^3.2.3", "dotenv": "^16.5.0", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^26.1.0", "sass": "^1.89.2", "tsx": "^4.20.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.2.3"}}